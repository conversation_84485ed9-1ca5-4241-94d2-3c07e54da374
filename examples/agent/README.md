# 自验证代理

一个严格的6步验证流程的AI代理，强调质量和准确性而非速度。

## 概述

`SelfValidatingAgent` 扩展了现有的 `ReactAgent` 功能，实现了一个全面的验证工作流程，通过系统性的自我审查和改进确保高质量的输出。

## 验证流程

该代理对每个问题都遵循以下6个步骤：

1. **初始解决方案** - 生成全面的首个解决方案，强调严谨性而非速度
2. **自我改进** - 优化和增强初始解决方案  
3. **验证** - 创建详细的错误报告，检查潜在问题
4. **错误审查** - 从错误报告中过滤出误报
5. **解决方案修复** - 解决有效的关注点并改进解决方案
6. **最终决策** - 接受或拒绝最终解决方案

## 文件说明

- `self_validating_agent.py` - 主要实现
- `demo_self_validation.py` - 交互式演示  
- `test_self_validation.py` - 测试套件和结构验证
- `README.md` - 本文档

## 快速开始

```python
from self_validating_agent import SelfValidatingAgent

# 创建代理
agent = SelfValidatingAgent(verbose=True)

# 使用验证进行求解
problem = "您的复杂问题在这里..."
result = agent.solve_with_validation_sync(problem)

# 检查结果
if result["success"] and result["is_accepted"]:
    print("高质量解决方案:", result["final_solution"])
else:
    print("解决方案被拒绝或失败")
```

## 使用示例

### 基本用法
```python
agent = SelfValidatingAgent()
result = agent.solve_with_validation_sync("计算1000美元在5%利率下3年的复利")
```

### 自定义配置
```python
agent = SelfValidatingAgent(
    model="gpt-4",
    temperature=0.1,
    max_iterations=15,
    verbose=True
)
```

### 访问验证步骤
```python
result = agent.solve_with_validation_sync(problem)

if result["success"]:
    steps = result["validation_steps"]
    print("初始解决方案:", steps["initial_solution"])
    print("错误报告:", steps["error_report"])
    print("最终解决方案:", steps["final_solution"])
    print("决策:", "接受" if result["is_accepted"] else "拒绝")
```

## 系统要求

- Python 3.11+
- 在 `OPENAI_API_KEY` 环境变量中设置 OpenAI API 密钥
- ai_building_blocks 包（来自父目录）

## 测试

运行测试套件：
```bash
python test_self_validation.py
```

运行演示（需要 API 密钥）：
```bash
python demo_self_validation.py
```

## 主要特性

- **严格流程**：6步验证确保全面分析
- **自我纠正**：识别并修复自身错误
- **透明性**：完整的推理链被跟踪并可用
- **质量关卡**：最终接受/拒绝决策防止低质量输出
- **可扩展**：基于现有的 ReactAgent 架构构建
- **可配置**：可自定义模型、温度和详细程度设置

## 架构

该实现利用现有的 `ai_building_blocks.agent` 组件：

- 扩展 `AgentClient` 进行 LLM 交互
- 为每个验证步骤使用专门的提示
- 通过 `ValidationState` 数据类维护状态
- 跟踪完整的推理链以实现可审计性

## 示例输出结构

```python
{
    "success": True,
    "problem": "原始问题陈述",
    "final_solution": "经过验证的最终解决方案",
    "is_accepted": True,
    "validation_steps": {
        "initial_solution": "首次尝试...",
        "improved_solution": "增强版本...",
        "error_report": "批判性分析...", 
        "reviewed_errors": "有效关注点...",
        "final_solution": "修正的最终版本..."
    },
    "reasoning_chain": [...],
    "process_summary": {
        "total_steps": 6,
        "final_status": "接受",
        "solution_length": 1250,
        "improvement_iterations": 3
    }
}
```

## 性能说明

- 每次验证运行进行6次 LLM 调用（每步一次）
- 典型执行时间：复杂问题需要2-3分钟
- 强调质量而非速度 - 用于高风险决策
- 默认温度设置为低（0.1）以保持一致性

## 何时使用

此代理适用于：

- 准确性至关重要的高风险问题解决
- 受益于多步验证的复杂推理任务
- 需要可审计性和透明性的情况
- 错误成本很高的问题

使用常规 `ReactAgent` 适用于：
- 快速原型设计和探索
- 速度比完美更重要的简单任务
- 不能接受2-3分钟延迟的交互式应用