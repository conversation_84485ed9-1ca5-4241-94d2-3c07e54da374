"""
自验证代理实现

本模块实现了一个遵循6步验证流程的自验证代理：
1. 初始解决方案生成 - 强调严谨性而非最终答案
2. 自我改进 - 尝试优化解决方案  
3. 解决方案验证 - 生成"错误报告"
4. 审查错误报告 - 删除错误的错误报告
5. 根据错误报告修正或改进解决方案
6. 接受或拒绝解决方案

该实现扩展了 ai_building_blocks 包中现有的 ReactAgent 功能。
"""

import os
import time
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass

from ai_building_blocks.agent import AgentClient


class ValidationStep(str, Enum):
    """自验证流程中的步骤。"""
    INITIAL_SOLUTION = "initial_solution"
    SELF_IMPROVEMENT = "self_improvement" 
    VALIDATION = "validation"
    ERROR_REVIEW = "error_review"
    SOLUTION_FIX = "solution_fix"
    FINAL_DECISION = "final_decision"


@dataclass
class ValidationState:
    """跟踪验证流程的状态。"""
    current_step: ValidationStep = ValidationStep.INITIAL_SOLUTION
    initial_solution: str = ""
    improved_solution: str = ""
    error_report: str = ""
    reviewed_errors: str = ""
    final_solution: str = ""
    is_accepted: bool = False
    reasoning_chain: List[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.reasoning_chain is None:
            self.reasoning_chain = []
    
    def add_reasoning(self, step: ValidationStep, content: str, result: str = ""):
        """添加一个推理步骤到链中。"""
        self.reasoning_chain.append({
            "step": step.value,
            "content": content,
            "result": result,
            "timestamp": time.time()
        })


class SelfValidatingAgent:
    """
    实现严格6步验证流程的自验证代理。
    
    该代理扩展了基本的 ReactAgent 功能，包含自验证和改进能力，
    通过系统性的自我审查和修正确保更高质量的输出。
    """
    
    def __init__(
        self,
        model: str = "gpt-4.1-nano",
        temperature: float = 0.1,
        max_iterations: int = 15,
        verbose: bool = True,
        api_key: Optional[str] = None
    ):
        """
        初始化自验证代理。
        
        参数:
            model: 使用的 OpenAI 模型
            temperature: 采样温度
            max_iterations: 基础代理的最大迭代次数
            verbose: 启用详细日志
            api_key: OpenAI API 密钥
        """
        self.verbose = verbose
        
        # 使用验证导向的系统消息创建基础代理
        self.base_agent = AgentClient(
            model=model,
            temperature=temperature,
            max_iterations=max_iterations,
            verbose=verbose,
            api_key=api_key,
            system_message=self._get_validation_system_message()
        )
    
    def _get_validation_system_message(self) -> str:
        """获取针对验证任务优化的系统消息。"""
        return """你是一个专注于严格问题解决和自我验证的高度分析型助手。

你的方法应该始终优先考虑：
1. 全面性而非速度
2. 基于证据的推理而非假设  
3. 多种视角和潜在的失败模式
4. 清晰、结构化的思维

生成解决方案时，要全面并逐步展示你的工作过程。
验证解决方案时，要批判性地寻找潜在错误、边缘情况和改进点。
审查错误报告时，要客观并区分有效关注点和误报。"""

    async def solve_with_validation(self, problem: str) -> Dict[str, Any]:
        """
        使用完整的6步验证流程解决问题。
        
        参数:
            problem: 要解决的问题陈述
            
        返回:
            包含完整验证流程结果的字典
        """
        if self.verbose:
            print(f"🔍 正在启动自验证流程: {problem[:100]}...")
        
        state = ValidationState()
        
        try:
            # 步骤1：生成初始解决方案
            await self._step1_initial_solution(problem, state)
            
            # 步骤2：自我改进
            await self._step2_self_improvement(state)
            
            # 步骤3：解决方案验证（生成错误报告）
            await self._step3_solution_validation(state)
            
            # 步骤4：审查错误报告
            await self._step4_error_review(state)
            
            # 步骤5：修复或改进解决方案
            await self._step5_solution_fix(state)
            
            # 步骤6：最终决策
            await self._step6_final_decision(state)
            
            return self._format_final_result(problem, state)
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 验证流程出错: {e}")
            return {
                "success": False,
                "error": str(e),
                "problem": problem,
                "state": state
            }
    
    def solve_with_validation_sync(self, problem: str) -> Dict[str, Any]:
        """验证流程的同步包装器。"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.solve_with_validation(problem))
        except RuntimeError:
            return asyncio.run(self.solve_with_validation(problem))
    
    async def _step1_initial_solution(self, problem: str, state: ValidationState) -> None:
        """步骤1：生成强调严谨性的初始解决方案。"""
        if self.verbose:
            print("📝 步骤1：生成初始解决方案...")
        
        prompt = f"""
        问题：{problem}
        
        请为这个问题提供一个全面、严谨的解决方案。重点关注：
        1. 理解问题的各个方面
        2. 考虑多种方法
        3. 逐步展示你的工作过程
        4. 要全面而不是急于得出最终答案
        5. 承认你所做的任何假设
        
        请花时间仔细思考 - 质量比速度更重要。
        """
        
        response = await self.base_agent.run_async(prompt)
        state.initial_solution = response.answer
        state.add_reasoning(
            ValidationStep.INITIAL_SOLUTION,
            "生成了全面的初始解决方案",
            state.initial_solution
        )
        
        if self.verbose:
            print(f"✅ 初始解决方案已生成（{len(state.initial_solution)} 字符）")
    
    async def _step2_self_improvement(self, state: ValidationState) -> None:
        """步骤2：尝试优化和改进初始解决方案。"""
        if self.verbose:
            print("🔧 步骤2：自我改进...")
        
        state.current_step = ValidationStep.SELF_IMPROVEMENT
        
        prompt = f"""
        原始解决方案：
        {state.initial_solution}
        
        请通过以下方式审查和改进这个解决方案：
        1. 识别任何差距或弱点
        2. 在需要的地方添加更多细节
        3. 考虑替代方法
        4. 使推理更清晰、更严谨
        5. 添加任何缺失的步骤或考虑因素
        
        提供一个解决这些问题的改进版本。
        """
        
        response = await self.base_agent.run_async(prompt)
        state.improved_solution = response.answer
        state.add_reasoning(
            ValidationStep.SELF_IMPROVEMENT,
            "分析并改进了初始解决方案",
            state.improved_solution
        )
        
        if self.verbose:
            print(f"✅ 解决方案已改进（{len(state.improved_solution)} 字符）")
    
    async def _step3_solution_validation(self, state: ValidationState) -> None:
        """步骤3：生成全面的错误报告。"""
        if self.verbose:
            print("🔍 步骤3：生成验证错误报告...")
        
        state.current_step = ValidationStep.VALIDATION
        
        prompt = f"""
        待验证的解决方案：
        {state.improved_solution}
        
        请通过批判性地检查以下方面来生成全面的错误报告：
        1. 逻辑错误或不一致
        2. 数学错误
        3. 缺失的步骤或不完整的推理
        4. 无根据的假设
        5. 未考虑的边缘情况
        6. 可能改变答案的替代解释
        7. 需要更多证据或理由的领域
        
        请彻底且批判性地检查 - 你的工作是找出潜在问题，而不是客气。
        列出具体问题并解释为什么它们是问题。
        """
        
        response = await self.base_agent.run_async(prompt)
        state.error_report = response.answer
        state.add_reasoning(
            ValidationStep.VALIDATION,
            "生成了全面的错误报告",
            state.error_report
        )
        
        if self.verbose:
            print(f"✅ 错误报告已生成（{len(state.error_report)} 字符）")
    
    async def _step4_error_review(self, state: ValidationState) -> None:
        """步骤4：审查错误报告以删除误报。"""
        if self.verbose:
            print("🔬 步骤4：审查错误报告...")
        
        state.current_step = ValidationStep.ERROR_REVIEW
        
        prompt = f"""
        待审查的错误报告：
        {state.error_report}
        
        被批评的原始解决方案：
        {state.improved_solution}
        
        请审查这个错误报告并：
        1. 识别哪些报告的错误是有效的，需要解决
        2. 识别哪些报告的错误是误报或误解
        3. 按重要性对有效错误进行优先级排序
        4. 提供一个只包含需要修复的合法问题的清单
        
        要客观公正 - 不要忽视有效的批评，但也不要接受错误的批评。
        """
        
        response = await self.base_agent.run_async(prompt)
        state.reviewed_errors = response.answer
        state.add_reasoning(
            ValidationStep.ERROR_REVIEW,
            "审查了错误报告的有效性",
            state.reviewed_errors
        )
        
        if self.verbose:
            print(f"✅ 错误报告已审查（{len(state.reviewed_errors)} 字符）")
    
    async def _step5_solution_fix(self, state: ValidationState) -> None:
        """步骤5：基于有效错误修复或改进解决方案。"""
        if self.verbose:
            print("🛠️ 步骤5：基于有效错误修复解决方案...")
        
        state.current_step = ValidationStep.SOLUTION_FIX
        
        prompt = f"""
        当前解决方案：
        {state.improved_solution}
        
        需要解决的有效错误：
        {state.reviewed_errors}
        
        请提供一个最终的、修正后的解决方案，该方案：
        1. 解决所有识别出的有效错误
        2. 保持当前解决方案的优点
        3. 清晰、完整且推理充分
        4. 包括任何必要的额外步骤或考虑因素
        
        这应该是你最好、最准确的解决方案。
        """
        
        response = await self.base_agent.run_async(prompt)
        state.final_solution = response.answer
        state.add_reasoning(
            ValidationStep.SOLUTION_FIX,
            "基于有效错误审查应用了修复",
            state.final_solution
        )
        
        if self.verbose:
            print(f"✅ 解决方案已修复（{len(state.final_solution)} 字符）")
    
    async def _step6_final_decision(self, state: ValidationState) -> None:
        """步骤6：做出最终接受/拒绝决定。"""
        if self.verbose:
            print("⚖️ 步骤6：做出最终接受/拒绝决定...")
        
        state.current_step = ValidationStep.FINAL_DECISION
        
        prompt = f"""
        待评估的最终解决方案：
        {state.final_solution}
        
        验证流程总结：
        - 初始解决方案已生成并改进
        - 错误报告已创建并审查
        - 解决方案已基于有效关注点进行修复
        
        请做出最终决定：这个解决方案应该被接受还是拒绝？
        
        考虑：
        1. 解决方案是否正确且完整？
        2. 推理是否合理且有充分支持？
        3. 是否仍存在重大缺陷或差距？
        4. 你是否有信心将其作为最终答案？
        
        请用"接受"或"拒绝"回复，并说明你的理由。
        """
        
        response = await self.base_agent.run_async(prompt)
        decision_text = response.answer
        
        # 解析决定
        state.is_accepted = "接受" in decision_text or decision_text.strip().upper().startswith("ACCEPT")
        
        state.add_reasoning(
            ValidationStep.FINAL_DECISION,
            f"最终决定：{'接受' if state.is_accepted else '拒绝'}",
            decision_text
        )
        
        if self.verbose:
            decision_emoji = "✅" if state.is_accepted else "❌"
            print(f"{decision_emoji} 最终决定：{'接受' if state.is_accepted else '拒绝'}")
    
    def _format_final_result(self, problem: str, state: ValidationState) -> Dict[str, Any]:
        """格式化验证流程的最终结果。"""
        return {
            "success": True,
            "problem": problem,
            "final_solution": state.final_solution,
            "is_accepted": state.is_accepted,
            "validation_steps": {
                "initial_solution": state.initial_solution,
                "improved_solution": state.improved_solution,
                "error_report": state.error_report,
                "reviewed_errors": state.reviewed_errors,
                "final_solution": state.final_solution
            },
            "reasoning_chain": state.reasoning_chain,
            "process_summary": {
                "total_steps": len(state.reasoning_chain),
                "final_status": "接受" if state.is_accepted else "拒绝",
                "solution_length": len(state.final_solution),
                "improvement_iterations": 3  # 初始 -> 改进 -> 最终
            }
        }


# 示例用法和演示
def main():
    """使用示例问题演示自验证代理。"""
    
    # 检查 API 密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 错误：OPENAI_API_KEY 环境变量未设置")
        print("请在 .env 文件或环境变量中设置你的 OpenAI API 密钥")
        return
    
    print("🤖 自验证代理演示")
    print("=" * 50)
    
    # 创建代理
    agent = SelfValidatingAgent(
        model="gpt-4",
        temperature=0.1,
        verbose=True
    )
    
    # 示例问题：数学推理问题
    problem = """
    一家公司有 120 名员工。 40% 在工程部，25% 在销售部，
    20% 在市场部，其余在管理部。
    
    如果公司决定将工程团队增加 50%，
    并将管理团队减少一半，那么变动后
    每个部门将有多少人？
    """
    
    print(f"📋 问题: {problem.strip()}")
    print("\n🔄 启动验证流程...\n")
    
    # 使用验证解决问题
    start_time = time.time()
    result = agent.solve_with_validation_sync(problem)
    execution_time = time.time() - start_time
    
    # 显示结果
    if result["success"]:
        print("📊 验证流程完成")
        print("=" * 50)
        
        print(f"\n⏱️ 总执行时间: {execution_time:.2f} 秒")
        print(f"🎯 最终状态: {result['process_summary']['final_status']}")
        print(f"📏 解决方案长度: {result['process_summary']['solution_length']} 字符")
        
        print(f"\n📝 最终解决方案:")
        print("-" * 30)
        print(result["final_solution"])
        
        print(f"\n🔍 流程总结:")
        print("-" * 30)
        for i, step in enumerate(result["reasoning_chain"], 1):
            step_name = step['step'].replace('_', ' ').title()
            step_name_cn = {
                "Initial Solution": "初始解决方案",
                "Self Improvement": "自我改进",
                "Validation": "验证",
                "Error Review": "错误审查",
                "Solution Fix": "解决方案修复",
                "Final Decision": "最终决定"
            }.get(step_name, step_name)
            print(f"{i}. {step_name_cn}")
        
        if result["is_accepted"]:
            print("\n✅ 解决方案在验证流程后被接受")
        else:
            print("\n❌ 解决方案在验证流程后被拒绝")
            
    else:
        print(f"\n❌ 验证流程失败: {result.get('error', '未知错误')}")


if __name__ == "__main__":
    main()