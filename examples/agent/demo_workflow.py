#!/usr/bin/env python3
"""没有API调用的自验证工作流程演示"""

import sys
sys.path.insert(0, '../..')

from self_validating_agent import SelfValidatingAgent, ValidationStep

def demonstrate_workflow():
    """演示自验证工作流程结构"""
    
    print("🤖 Self-Validating Agent Workflow Demonstration")
    print("=" * 60)
    
    # Create agent instance
    agent = SelfValidatingAgent(verbose=True)
    
    # Example problem
    problem = "Calculate the perimeter of a rectangle with length 15m and width 8m"
    
    print(f"\n📋 Problem: {problem}")
    print("\n🔄 Self-Validation Process:")
    print("-" * 40)
    
    # Demonstrate each step
    steps = [
        (ValidationStep.INITIAL_SOLUTION, "📝", 
         "周长 = 2 × (长 + 宽) = 2 × (15 + 8) = 2 × 23 = 46m"),
        
        (ValidationStep.SELF_IMPROVEMENT, "🔧", 
         "周长 = 2 × (15m + 8m) = 2 × 23m = 46m。这是矩形周围的总距离。"),
        
        (ValidationStep.VALIDATION, "🔍", 
         "潜在问题: 1) 最终答案中可能缺少单位, 2) 需要验证公式"),
        
        (ValidationStep.ERROR_REVIEW, "🔬", 
         "有效错误: 单位已正确包含。无效错误: 公式是正确的。"),
        
        (ValidationStep.SOLUTION_FIX, "🛠️", 
         "最终: 矩形的周长是46米。"),
        
        (ValidationStep.FINAL_DECISION, "⚖️", 
         "接受 - 解决方案在数学上是正确的，单位也合适。")
    ]
    
    # 模拟工作流程
    for step, icon, output in steps:
        print(f"\n{icon} 步骤 {step.value}: {step.name}")
        print(f"   输出: {output[:80]}{'...' if len(output) > 80 else ''}")
    
    print("\n" + "=" * 60)
    print("✅ 验证完成！")
    print("\n📊 结果:")
    print(f"   • 最终解决方案: 周长是46米")
    print(f"   • 状态: 接受")
    print(f"   • 置信度: 高")
    
    print("\n💡 自验证的好处:")
    print("   • 捕获计算错误")
    print("   • 确保解决方案完整")
    print("   • 提高答案质量")
    print("   • 提供透明度")
    
    print("\n🚀 使用真实LLM运行:")
    print("   1. 确保设置了 OPENAI_API_KEY")
    print("   2. 检查API配额/限制")
    print("   3. 运行: agent.solve_with_validation_sync(problem)")

if __name__ == "__main__":
    demonstrate_workflow()