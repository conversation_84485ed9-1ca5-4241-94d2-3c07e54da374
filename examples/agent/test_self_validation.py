#!/usr/bin/env python3
"""
SelfValidatingAgent 实现的快速测试。
"""

import sys
import os

# 添加导入路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))
sys.path.insert(0, os.path.dirname(__file__))

from self_validating_agent import SelfValidatingAgent, ValidationStep, ValidationState


def test_structure():
    """测试基本结构和组件。"""
    
    print("🧪 测试 SelfValidatingAgent 结构")
    print("=" * 50)
    
    # 测试1：创建实例
    print("1. 测试实例化...")
    agent = SelfValidatingAgent(verbose=False)
    state = ValidationState()
    print("   ✓ 代理和状态创建成功")
    
    # 测试2：检查验证步骤
    print("2. 测试验证步骤枚举...")
    expected_steps = [
        "initial_solution", "self_improvement", "validation", 
        "error_review", "solution_fix", "final_decision"
    ]
    actual_steps = [step.value for step in ValidationStep]
    assert actual_steps == expected_steps, f"期望 {expected_steps}，得到 {actual_steps}"
    print("   ✓ 所有6个验证步骤存在且正确")
    
    # 测试3：检查状态管理
    print("3. 测试状态管理...")
    state.add_reasoning(ValidationStep.INITIAL_SOLUTION, "测试推理", "测试结果")
    assert len(state.reasoning_chain) == 1
    assert state.reasoning_chain[0]["step"] == "initial_solution"
    print("   ✓ 状态跟踪正常工作")
    
    # 测试4：检查代理配置
    print("4. 测试代理配置...")
    assert hasattr(agent, 'base_agent'), "代理应该有 base_agent 属性"
    assert hasattr(agent, 'verbose'), "代理应该有 verbose 属性"
    print("   ✓ 代理配置正确")
    
    print("\n✅ 所有结构测试通过！")
    return True


def test_workflow_methods():
    """测试所有工作流方法存在并可调用。"""
    
    print("\n🔧 测试工作流方法")
    print("=" * 50)
    
    agent = SelfValidatingAgent(verbose=False)
    
    # 检查所有必需方法存在
    required_methods = [
        '_step1_initial_solution',
        '_step2_self_improvement', 
        '_step3_solution_validation',
        '_step4_error_review',
        '_step5_solution_fix',
        '_step6_final_decision'
    ]
    
    for method_name in required_methods:
        assert hasattr(agent, method_name), f"未找到方法 {method_name}"
        method = getattr(agent, method_name)
        assert callable(method), f"方法 {method_name} 不可调用"
        print(f"   ✓ {method_name}")
    
    # 检查主要方法
    assert hasattr(agent, 'solve_with_validation'), "缺少 solve_with_validation 方法"
    assert hasattr(agent, 'solve_with_validation_sync'), "缺少 solve_with_validation_sync 方法"
    print("   ✓ solve_with_validation")
    print("   ✓ solve_with_validation_sync")
    
    print("\n✅ 所有工作流方法存在！")
    return True


def demonstrate_without_api():
    """不需要API访问权限就能演示验证流程结构。"""
    
    print("\n📋 验证流程演示")
    print("=" * 50)
    
    print("SelfValidatingAgent 实现了6步验证流程：")
    print()
    
    steps = [
        ("初始解决方案", "生成全面的首个解决方案，强调严格性"),
        ("自我改进", "优化和增强初始解决方案"),
        ("验证", "创建详细的错误报告，检查潜在问题"),
        ("错误审查", "从错误报告中过滤出误报"),
        ("解决方案修复", "解决有效关注点并改进解决方案"),
        ("最终决策", "接受或拒绝最终解决方案")
    ]
    
    for i, (name, description) in enumerate(steps, 1):
        print(f"{i}. {name:15} - {description}")
    
    print()
    print("💡 主要特性：")
    print("   • 使用现有的ReactAgent作为基础，带有专门的提示")
    print("   • 每一步都传递给下一步，实现迭代改进")
    print("   • 跟踪完整的推理链以保证透明度")
    print("   • 最终决策为输出提供质量门禁")
    print("   • 强调严格性和自我纠错而非速度")
    
    print()
    print("🔄 示例用法：")
    print("   agent = SelfValidatingAgent()")
    print("   result = agent.solve_with_validation_sync('您的问题在这里')")
    print("   if result['success'] and result['is_accepted']:")
    print("       print('高质量解决方案:', result['final_solution'])")


def main():
    """运行所有测试和演示。"""
    
    print("🤖 SelfValidatingAgent 测试套件")
    print("=" * 60)
    
    try:
        # 运行结构测试
        test_structure()
        
        # 运行方法测试  
        test_workflow_methods()
        
        # 显示演示
        demonstrate_without_api()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过 - 实现就绪！")
        print("=" * 60)
        
        # 检查 API 密钥
        if os.getenv("OPENAI_API_KEY"):
            print("\n🔑 检测到 OPENAI_API_KEY - 已准备好进行完整验证运行")
        else:
            print("\n💡 要使用真实问题进行测试，请设置 OPENAI_API_KEY 环境变量")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)