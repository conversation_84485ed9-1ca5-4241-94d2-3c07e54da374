#!/usr/bin/env python3
"""
展示 SelfValidatingAgent 工作流程的简单示例。

此脚本演示如何使用自验证代理进行严格的问题解决。
"""

import os
import sys

# 将 src 目录添加到路径中，以便我们可以从 ai_building_blocks 导入
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))

# 从同一目录导入自验证代理
sys.path.insert(0, os.path.dirname(__file__))
from self_validating_agent import SelfValidatingAgent


def demo_validation_workflow():
    """使用简单示例演示验证工作流程。"""
    
    print("🤖 自验证代理演示")
    print("=" * 60)
    print()
    
    # 检查是否有 API 密钥
    has_api_key = bool(os.getenv("OPENAI_API_KEY"))
    
    if not has_api_key:
        print("ℹ️  未找到 OPENAI_API_KEY - 仅显示结构演示")
        print("   要运行完整演示，请在环境中设置 OPENAI_API_KEY")
        print()
    
    # 创建代理
    agent = SelfValidatingAgent(
        model="gpt-4.1-nano",
        temperature=0.1,
        verbose=True
    )
    
    # 可选择的示例问题
    problems = {
        "math": """
        一个矩形花园长15米，宽8米。
        如果要在周围围栅栏，栅栏每米成本12美元，
        总成本是多少？
        """,
        
        "logic": """
        你有12个看起来完全相同的球，但其中一个稍微重一些。
        你有一个天平，可以使用3次。你怎样找到那个重球？
        """,
        
        "optimization": """
        一家公司需要在2天内在3个会议室安排5个会议。
        每个会议需要2小时。每个房间每天可用8小时。
        最优的时间安排是什么以最小化冲突？
        """
    }
    
    print("可用的示例问题:")
    for key, problem in problems.items():
        print(f"  {key}: {problem.strip()[:80]}...")
    print()
    
    # 使用数学问题作为默认
    selected_problem = problems["math"]
    
    print(f"📋 选择的问题（数学）:")
    print("-" * 40)
    print(selected_problem.strip())
    print()
    
    if has_api_key:
        print("🔄 运行完整验证流程...")
        print("   这可能需要2-3分钟，因为涉及多个LLM调用")
        print()
        
        try:
            result = agent.solve_with_validation_sync(selected_problem)
            
            if result["success"]:
                print("\n" + "=" * 60)
                print("📊 验证流程结果")
                print("=" * 60)
                
                # 显示最终解决方案
                print(f"\n🎯 最终状态: {result['process_summary']['final_status']}")
                print(f"📏 解决方案长度: {result['process_summary']['solution_length']} 字符")
                
                print(f"\n📝 最终解决方案:")
                print("-" * 40)
                print(result["final_solution"])
                
                # 显示流程步骤
                print(f"\n🔍 验证流程步骤:")
                print("-" * 40)
                for i, step in enumerate(result["reasoning_chain"], 1):
                    step_name = step['step'].replace('_', ' ').title()
                    print(f"{i}. {step_name}")
                
                # 显示是否被接受
                if result["is_accepted"]:
                    print("\n✅ 解决方案在全面验证后被接受")
                else:
                    print("\n❌ 解决方案被拒绝 - 需要更多工作")
                    
            else:
                print(f"\n❌ 流程失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"\n❌ 运行验证时出错: {e}")
            print("   这可能是由于API速率限制或网络问题")
    
    else:
        print("🔍 验证流程结构（6步）:")
        print("-" * 40)
        print("1. 初始解决方案    - 生成全面的首个解决方案")
        print("2. 自我改进       - 优化和增强解决方案")  
        print("3. 验证          - 创建详细的错误报告")
        print("4. 错误审查       - 过滤出误报")
        print("5. 解决方案修复    - 解决有效关注点")
        print("6. 最终决策       - 接受或拒绝解决方案")
        print()
        print("💡 每一步都使用带有专门提示的基础ReactAgent")
        print("   以确保严格的分析和改进")
    
    print("\n" + "=" * 60)
    print("演示完成！🎉")


def show_usage_examples():
    """展示使用 SelfValidatingAgent 的不同方式。"""
    
    print("\n📚 使用示例:")
    print("-" * 40)
    
    print("""
# 基本用法
agent = SelfValidatingAgent()
result = agent.solve_with_validation_sync("您的问题在这里")

# 自定义设置
agent = SelfValidatingAgent(
    model="gpt-4",
    temperature=0.1,
    verbose=True
)

# 访问验证步骤
result = agent.solve_with_validation_sync(problem)
if result["success"]:
    print("最终解决方案:", result["final_solution"])
    print("是否被接受:", result["is_accepted"])
    
    # 访问每个验证步骤
    steps = result["validation_steps"]
    print("初始:", steps["initial_solution"])
    print("改进:", steps["improved_solution"])
    print("发现的错误:", steps["error_report"])
    print("有效错误:", steps["reviewed_errors"])
""")


if __name__ == "__main__":
    demo_validation_workflow()
    show_usage_examples()