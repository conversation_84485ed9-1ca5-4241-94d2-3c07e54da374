# 自我验证代理

## 概述

自我验证代理是一个基于6步验证流程的AI代理系统，旨在通过严格的自我审查和改进来确保高质量的问题解决方案。

## 自我验证流程

### 步骤1：初始解决方案生成
强调严谨性而非速度，生成全面、深思熟虑的初始解决方案。

### 步骤2：自我改进
分析初始解决方案的不足，进行优化和增强，提高解决方案的质量。

### 步骤3：验证解决方案
对解决方案进行批判性审查，生成详细的"错误报告"，识别潜在问题。

### 步骤4：审查错误报告
客观评估错误报告，过滤出误报，保留真正需要解决的问题。

### 步骤5：解决方案修正
根据有效的错误报告，对解决方案进行修正和改进。

### 步骤6：最终决策
对修正后的解决方案进行最终评估，决定接受或拒绝。

## 核心优势

- **质量保证**：多步验证确保解决方案的准确性和完整性
- **自我纠错**：系统能够识别并修正自身的错误
- **透明度**：完整的推理链提供可追溯的决策过程
- **严谨性**：优先考虑准确性而非速度

## 适用场景

- 高风险决策场景
- 复杂问题求解
- 需要高度准确性的任务
- 要求可审计性的应用

## 技术实现

基于现有的ReactAgent架构，通过专门的提示和状态管理实现6步验证流程。