{"permissions": {"allow": ["Bash(find:*)", "Bash(pytest:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uv:*)", "Bash(git init:*)", "Bash(ls:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(OPENAI_API_KEY=\"\" python -c \"\nimport sys, os\nsys.path.insert(0, ''src'')\nsys.path.insert(0, ''examples/agent'')\n\nfrom self_validating_agent import SelfValidatingAgent\n\nprint(''Testing SelfValidatingAgent without API key...'')\nagent = SelfValidatingAgent(verbose=True)\nprint(''✓ Agent created successfully'')\nprint(''✓ Ready to demonstrate validation workflow structure'')\n\")", "Bash(grep:*)", "Bash(OPENAI_API_KEY=\"\" python -c \"\nimport sys, os\nsys.path.insert(0, ''src'')\nsys.path.insert(0, ''examples/agent'')\n\nfrom self_validating_agent import SelfValidatingAgent\n\nprint(''Testing SelfValidatingAgent without API key...'')\nagent = SelfValidatingAgent(verbose=True)\nprint(''✓ Agent created successfully'')\nprint(''✓ Ready to demonstrate validation workflow structure'')\n\")"], "deny": []}}